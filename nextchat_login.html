<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - NextChat Style</title>
    
    <!-- 核心技术栈 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* 设计系统基础 */
        body {
            font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, sans-serif;
            min-height: 100vh;
            transition: all 0.3s ease;
        }
        
        /* 浅色模式变量 */
        .light-mode {
            --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-tertiary: #94a3b8;
            --border-color: #e2e8f0;
            --border-focus: #3b82f6;
            --accent-primary: #3b82f6;
            --accent-hover: #2563eb;
            --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        /* 深色模式变量 */
        .dark-mode {
            --bg-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            --bg-primary: #1e293b;
            --bg-secondary: #334155;
            --text-primary: #f1f5f9;
            --text-secondary: #cbd5e1;
            --text-tertiary: #94a3b8;
            --border-color: #475569;
            --border-focus: #60a5fa;
            --accent-primary: #3b82f6;
            --accent-hover: #60a5fa;
            --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
            --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
        }
        
        body {
            background: var(--bg-gradient);
        }
        
        /* 卡片样式 - 光影与深度 */
        .login-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-medium), 0 4px 6px -1px rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(10px);
        }
        
        /* 简约优雅的交互反馈 */
        .interactive-element {
            transition: all 0.2s ease;
        }
        
        .interactive-element:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }
        
        /* 返回按钮样式 */
        .back-button {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            box-shadow: var(--shadow-light);
        }
        
        .back-button:hover {
            color: var(--text-primary);
            border-color: var(--border-focus);
        }
        
        /* 主题切换按钮 */
        .theme-toggle {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            box-shadow: var(--shadow-light);
        }
        
        .theme-toggle:hover {
            color: var(--text-primary);
            border-color: var(--border-focus);
        }
        
        /* 品牌渐变 - 适配深浅模式 */
        .brand-gradient {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-hover) 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        /* 输入框样式 */
        .form-input {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            transition: all 0.2s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--border-focus);
            background: var(--bg-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-input::placeholder {
            color: var(--text-tertiary);
        }
        
        /* 按钮样式 */
        .btn-primary {
            background: var(--accent-primary);
            color: white;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            background: var(--accent-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }
        
        .btn-secondary {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .btn-secondary:hover {
            border-color: var(--border-focus);
            background: var(--bg-secondary);
        }
        
        /* 简约动画 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.4s ease-out;
        }
        
        /* 8px网格系统 */
        .spacing-system {
            padding: 2rem;
        }
        
        /* 响应式适配 */
        @media (max-width: 640px) {
            .spacing-system {
                padding: 1.5rem;
            }
            
            .login-card {
                margin: 1rem;
                width: calc(100% - 2rem);
            }
            
            .theme-toggle {
                top: 1rem;
                right: 1rem;
            }
            
            .back-button {
                top: 1rem;
                left: 1rem;
            }
        }
    </style>
</head>

<body class="light-mode flex items-center justify-center min-h-screen p-4">
    
    <!-- 主题切换按钮 -->
    <button id="themeToggle" class="theme-toggle interactive-element fixed top-6 right-6 w-10 h-10 rounded-full flex items-center justify-center">
        <i id="themeIcon" class="fas fa-moon text-sm"></i>
    </button>
    
    <!-- 返回按钮 -->
    <button class="back-button interactive-element fixed top-6 left-6 w-10 h-10 rounded-full flex items-center justify-center">
        <i class="fas fa-arrow-left text-sm"></i>
    </button>
    
    <!-- 登录卡片 -->
    <div class="login-card rounded-2xl w-full max-w-md animate-fade-in">
        <div class="spacing-system">
            
            <!-- 品牌展示区域 -->
            <div class="text-center mb-8">
                <!-- Logo区域 -->
                <div class="flex items-center justify-center mb-6">
                    <div class="w-12 h-12 rounded-2xl flex items-center justify-center mr-3 shadow-lg" 
                         style="background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));">
                        <i class="fas fa-comments text-white text-xl"></i>
                    </div>
                    <h1 class="text-3xl font-bold brand-gradient">NextChat</h1>
                </div>

                <!-- 问候语 -->
                <div id="welcomeText">
                    <h2 class="text-2xl font-semibold mb-2" style="color: var(--text-primary);">Hi, I'm NextChat</h2>
                    <p class="text-sm" style="color: var(--text-secondary);">AI At Model No Subscription No Tricks</p>
                </div>
            </div>

            <!-- 登录表单 -->
            <form id="loginForm" class="space-y-6">
                <!-- 邮箱输入 -->
                <div>
                    <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">邮箱地址</label>
                    <input type="email" 
                           id="loginEmail"
                           placeholder="Enter Email" 
                           class="form-input w-full px-4 py-3 rounded-xl">
                </div>

                <!-- 验证码 -->
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">验证码</label>
                        <input type="text" 
                               id="loginCode"
                               placeholder="6-digit Code" 
                               class="form-input w-full px-4 py-3 rounded-xl">
                    </div>
                    <div class="flex items-end">
                        <button type="button" 
                                id="loginSendCode"
                                class="btn-secondary interactive-element w-full py-3 rounded-xl">
                            Send Code
                        </button>
                    </div>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" 
                        class="btn-primary interactive-element w-full py-3 rounded-xl text-base">
                    Login
                </button>

                <!-- 注册链接 -->
                <div class="text-center mt-4">
                    <p class="text-sm" style="color: var(--text-secondary);">
                        没有账户？ 
                        <button type="button" id="showRegister" 
                                style="color: var(--accent-primary);" 
                                class="hover:opacity-80 transition-opacity font-medium">
                            立即注册
                        </button>
                    </p>
                </div>

                <!-- 隐私政策 -->
                <p class="text-center text-xs mt-4" style="color: var(--text-tertiary);">
                    By continuing, you agree to 
                    <a href="#" style="color: var(--accent-primary);" class="hover:opacity-80 transition-opacity">Privacy Policy</a>
                </p>
            </form>

            <!-- 注册表单 -->
            <form id="registerForm" class="space-y-6 hidden">
                <!-- 用户名输入 -->
                <div>
                    <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">用户名</label>
                    <input type="text" 
                           id="registerUsername"
                           placeholder="Enter Username" 
                           class="form-input w-full px-4 py-3 rounded-xl">
                </div>

                <!-- 邮箱输入 -->
                <div>
                    <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">邮箱地址</label>
                    <input type="email" 
                           id="registerEmail"
                           placeholder="Enter Email" 
                           class="form-input w-full px-4 py-3 rounded-xl">
                </div>

                <!-- 验证码 -->
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-sm font-medium mb-2" style="color: var(--text-secondary);">验证码</label>
                        <input type="text" 
                               id="registerCode"
                               placeholder="6-digit Code" 
                               class="form-input w-full px-4 py-3 rounded-xl">
                    </div>
                    <div class="flex items-end">
                        <button type="button" 
                                id="registerSendCode"
                                class="btn-secondary interactive-element w-full py-3 rounded-xl">
                            Send Code
                        </button>
                    </div>
                </div>

                <!-- 注册按钮 -->
                <button type="submit" 
                        class="btn-primary interactive-element w-full py-3 rounded-xl text-base">
                    Register
                </button>

                <!-- 登录链接 -->
                <div class="text-center mt-4">
                    <p class="text-sm" style="color: var(--text-secondary);">
                        已有账户？ 
                        <button type="button" id="showLogin" 
                                style="color: var(--accent-primary);" 
                                class="hover:opacity-80 transition-opacity font-medium">
                            立即登录
                        </button>
                    </p>
                </div>

                <!-- 隐私政策 -->
                <p class="text-center text-xs mt-4" style="color: var(--text-tertiary);">
                    By registering, you agree to 
                    <a href="#" style="color: var(--accent-primary);" class="hover:opacity-80 transition-opacity">Privacy Policy</a>
                </p>
            </form>
        </div>
    </div>

    <script>
        // 主题切换功能
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.getElementById('themeIcon');
        const body = document.body;
        
        // 检查本地存储的主题偏好
        const savedTheme = localStorage.getItem('theme') || 'light';
        setTheme(savedTheme);
        
        themeToggle.addEventListener('click', () => {
            const currentTheme = body.classList.contains('dark-mode') ? 'dark' : 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);
            localStorage.setItem('theme', newTheme);
        });
        
        function setTheme(theme) {
            if (theme === 'dark') {
                body.className = 'dark-mode flex items-center justify-center min-h-screen p-4';
                themeIcon.className = 'fas fa-sun text-sm';
            } else {
                body.className = 'light-mode flex items-center justify-center min-h-screen p-4';
                themeIcon.className = 'fas fa-moon text-sm';
            }
        }
        
        // 表单交互增强
        const inputs = document.querySelectorAll('.form-input');
        const sendCodeBtn = document.querySelector('button[type="button"]');
        const emailInput = document.querySelector('input[type="email"]');
        const backButton = document.querySelector('.back-button');
        
        // 发送验证码功能
        sendCodeBtn.addEventListener('click', function() {
            const email = emailInput.value.trim();
            
            if (!email) {
                emailInput.focus();
                emailInput.style.borderColor = '#ef4444';
                emailInput.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
                setTimeout(() => {
                    emailInput.style.borderColor = 'var(--border-color)';
                    emailInput.style.boxShadow = 'none';
                }, 2000);
                return;
            }
            
            // 发送状态
            const originalText = this.textContent;
            this.disabled = true;
            this.textContent = 'Sending...';
            this.style.opacity = '0.7';
            
            setTimeout(() => {
                this.textContent = 'Sent ✓';
                this.style.color = '#10b981';
                
                setTimeout(() => {
                    this.disabled = false;
                    this.textContent = originalText;
                    this.style.opacity = '1';
                    this.style.color = '';
                }, 2000);
            }, 1500);
        });
        
        // 返回按钮功能
        backButton.addEventListener('click', function() {
            // 这里可以添加返回逻辑，比如：
            // window.history.back();
            // 或者跳转到指定页面
            console.log('返回上一页');
        });
        
        // 表单提交处理
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = emailInput.value.trim();
            const code = document.querySelector('input[placeholder="6-digit Code"]').value.trim();
            
            if (!email || !code) {
                return;
            }
            
            // 这里添加登录逻辑
            console.log('登录处理', { email, code });
        });
        
        // 简约的键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                backButton.click();
            }
        });
    </script>
</body>
</html>