// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户角色枚举
enum Role {
  USER
  ADMIN
}

// 用户表
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String   @unique
  password  String?  // bcrypt hash - 可选，支持无密码登录
  role      Role     @default(USER)
  enabled   Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 计费相关字段
  totalTokensUsed    Int     @default(0)    // 总使用token数
  totalCost          Float   @default(0.0)  // 总成本
  monthlyTokensUsed  Int     @default(0)    // 当月使用token数
  monthlyCost        Float   @default(0.0)  // 当月成本
  lastResetAt        DateTime @default(now()) // 上次重置时间

  // 关联的API调用日志
  apiLogs   ApiLog[]
  // 关联的用户会话
  sessions  UserSession[]
  // 关联的用户统计
  userStats UserStats[]

  @@map("users")
}

// 邮箱验证码表
model EmailVerification {
  id        String   @id @default(cuid())
  email     String   // 邮箱地址
  code      String   // 验证码
  type      String   // 验证类型：login, register
  expiresAt DateTime // 过期时间
  used      Boolean  @default(false) // 是否已使用
  createdAt DateTime @default(now())

  @@map("email_verifications")
}

// 服务商配置表
model Provider {
  id          String   @id @default(cuid())
  name        String   // 显示名称，如 "OpenAI GPT-4"
  type        String   // 服务商类型：openai, google, anthropic, etc.
  baseUrl     String   // API基础URL
  apiKey      String   // 加密存储的API Key
  enabled     Boolean  @default(true)
  config      String?  // JSON格式的额外配置
  models      String?  // JSON格式的支持模型列表
  description String?  // 描述信息
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联的API调用日志
  apiLogs     ApiLog[]

  @@map("providers")
}

// 系统配置表
model SystemConfig {
  key         String   @id
  value       String   // 配置值
  type        String   // 配置类型：string, number, boolean, json
  description String?  // 配置描述
  category    String?  // 配置分类
  updatedAt   DateTime @updatedAt

  @@map("system_config")
}

// API调用日志表
model ApiLog {
  id               String   @id @default(cuid())
  userId           String?  // 用户ID，可为空（匿名用户）
  providerId       String   // 服务商ID
  model            String   // 使用的模型
  endpoint         String   // 调用的端点
  method           String   // HTTP方法
  tokensUsed       Int?     // 使用的token数量（总数）
  promptTokens     Int?     // 输入token数量
  completionTokens Int?     // 输出token数量
  cost             Float?   // 成本（如果有）
  status           String   // 状态：success, error, timeout
  errorMsg         String?  // 错误信息
  duration         Int?     // 请求耗时（毫秒）
  requestBody      String?  // 请求体（JSON字符串，用于调试）
  responseBody     String?  // 响应体关键信息（JSON字符串，用于调试）
  createdAt        DateTime @default(now())

  // 关联关系
  user        User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  provider    Provider  @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@map("api_logs")
}

// 用户会话表（可选，用于存储聊天会话）
model UserSession {
  id          String   @id @default(cuid())
  userId      String?  // 用户ID，可为空（匿名用户）
  sessionId   String   @unique // 前端会话ID
  title       String?  // 会话标题
  sessionData String   // JSON格式的会话数据
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("user_sessions")
}

// 用户统计表（按日/月统计）
model UserStats {
  id              String   @id @default(cuid())
  userId          String   // 用户ID
  date            DateTime // 统计日期（按天）
  tokensUsed      Int      @default(0) // 当日使用token数
  promptTokens    Int      @default(0) // 当日输入token数
  completionTokens Int     @default(0) // 当日输出token数
  cost            Float    @default(0.0) // 当日成本
  apiCalls        Int      @default(0) // 当日API调用次数
  successCalls    Int      @default(0) // 当日成功调用次数
  errorCalls      Int      @default(0) // 当日失败调用次数
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // 关联关系
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@map("user_stats")
}

// 模型定价表
model ModelPricing {
  id                String   @id @default(cuid())
  model             String   @unique // 模型名称
  provider          String   // 服务商类型
  inputTokenPrice   Float    // 输入token价格（每1000个token）
  outputTokenPrice  Float    // 输出token价格（每1000个token）
  enabled           Boolean  @default(true)
  description       String?  // 描述
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("model_pricing")
}

// 系统统计表（全局统计）
model SystemStats {
  id               String   @id @default(cuid())
  date             DateTime @unique // 统计日期（按天）
  totalUsers       Int      @default(0) // 总用户数
  activeUsers      Int      @default(0) // 活跃用户数
  totalApiCalls    Int      @default(0) // 总API调用次数
  successApiCalls  Int      @default(0) // 成功API调用次数
  totalTokens      Int      @default(0) // 总token使用量
  totalCost        Float    @default(0.0) // 总成本
  totalRevenue     Float    @default(0.0) // 总收入（如果有）
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@map("system_stats")
}
