import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import { verifyPassword } from "@/app/lib/encryption";
import { generateToken } from "@/app/lib/jwt";
import { isValidEmail } from "@/app/lib/email";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { email, code, username, password } = body;

    // 支持两种登录方式：邮箱验证码登录 或 用户名密码登录
    if (email && code) {
      // 邮箱验证码登录
      return await handleEmailCodeLogin(email, code);
    } else if (username && password) {
      // 传统用户名密码登录
      return await handlePasswordLogin(username, password);
    } else {
      return NextResponse.json(
        { error: "请提供邮箱验证码或用户名密码" },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "登录失败，请稍后重试" },
      { status: 500 },
    );
  }
}

// 邮箱验证码登录
async function handleEmailCodeLogin(email: string, code: string) {
  // 验证邮箱格式
  if (!isValidEmail(email)) {
    return NextResponse.json(
      { error: "邮箱格式不正确" },
      { status: 400 }
    );
  }

  // 验证验证码
  const verification = await prisma.emailVerification.findFirst({
    where: {
      email,
      code,
      type: 'login',
      used: false,
      expiresAt: {
        gt: new Date(),
      },
    },
  });

  if (!verification) {
    return NextResponse.json(
      { error: "验证码无效或已过期" },
      { status: 401 }
    );
  }

  // 查找用户
  const user = await prisma.user.findUnique({
    where: {
      email,
      enabled: true,
    },
  });

  if (!user) {
    return NextResponse.json(
      { error: "用户不存在或已被禁用" },
      { status: 401 }
    );
  }

  // 标记验证码为已使用
  await prisma.emailVerification.update({
    where: {
      id: verification.id,
    },
    data: {
      used: true,
    },
  });

  return generateLoginResponse(user);
}

// 传统用户名密码登录
async function handlePasswordLogin(username: string, password: string) {
  // 查找用户（支持用户名或邮箱登录）
  const user = await prisma.user.findFirst({
    where: {
      OR: [{ username: username }, { email: username }],
      enabled: true,
    },
  });

  if (!user) {
    return NextResponse.json({ error: "用户名或密码错误" }, { status: 401 });
  }

  // 验证密码（如果用户有密码）
  if (!user.password) {
    return NextResponse.json(
      { error: "该账户仅支持邮箱验证码登录" },
      { status: 401 }
    );
  }

  const isPasswordValid = verifyPassword(password, user.password);
  if (!isPasswordValid) {
    return NextResponse.json({ error: "用户名或密码错误" }, { status: 401 });
  }

  return generateLoginResponse(user);
}

// 生成登录响应
async function generateLoginResponse(user: any) {

  // 生成JWT token
  const token = generateToken({
    userId: user.id,
    username: user.username,
    email: user.email,
    role: user.role as "USER" | "ADMIN",
  });

  // 返回用户信息和token
  const response = NextResponse.json({
    success: true,
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
    },
    token,
  });

  // 设置cookie
  response.cookies.set("auth-token", token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    maxAge: 7 * 24 * 60 * 60, // 7 days
  });

  return response;
}

export const runtime = "nodejs";
