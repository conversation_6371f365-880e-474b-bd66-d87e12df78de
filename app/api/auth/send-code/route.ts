import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import { sendVerificationCode, generateVerificationCode, isValidEmail } from "@/app/lib/email";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { email, type } = body;

    // 验证输入
    if (!email || !type) {
      return NextResponse.json(
        { error: "邮箱和验证类型不能为空" },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: "邮箱格式不正确" },
        { status: 400 }
      );
    }

    // 验证类型
    if (!['login', 'register'].includes(type)) {
      return NextResponse.json(
        { error: "验证类型无效" },
        { status: 400 }
      );
    }

    // 检查邮箱是否存在（根据类型判断）
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (type === 'login' && !existingUser) {
      return NextResponse.json(
        { error: "该邮箱尚未注册" },
        { status: 404 }
      );
    }

    if (type === 'register' && existingUser) {
      return NextResponse.json(
        { error: "该邮箱已被注册" },
        { status: 409 }
      );
    }

    // 检查是否允许注册（仅注册时检查）
    if (type === 'register') {
      const allowRegistration = await prisma.systemConfig.findUnique({
        where: { key: "allowUserRegistration" },
      });

      if (allowRegistration?.value === "false") {
        return NextResponse.json(
          { error: "当前不允许用户注册" },
          { status: 403 }
        );
      }
    }

    // 检查发送频率限制（5分钟内只能发送一次）
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const recentCode = await prisma.emailVerification.findFirst({
      where: {
        email,
        type,
        createdAt: {
          gte: fiveMinutesAgo,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    if (recentCode) {
      const remainingTime = Math.ceil((recentCode.createdAt.getTime() + 5 * 60 * 1000 - Date.now()) / 1000);
      return NextResponse.json(
        { error: `请等待 ${remainingTime} 秒后再次发送验证码` },
        { status: 429 }
      );
    }

    // 生成验证码
    const code = generateVerificationCode();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟后过期

    // 删除该邮箱的旧验证码
    await prisma.emailVerification.deleteMany({
      where: {
        email,
        type,
      },
    });

    // 存储新验证码
    await prisma.emailVerification.create({
      data: {
        email,
        code,
        type,
        expiresAt,
      },
    });

    // 发送邮件
    try {
      await sendVerificationCode(email, code, type as 'login' | 'register');
    } catch (emailError) {
      console.error('邮件发送失败:', emailError);
      
      // 删除刚创建的验证码记录
      await prisma.emailVerification.deleteMany({
        where: {
          email,
          code,
          type,
        },
      });

      return NextResponse.json(
        { error: "验证码发送失败，请稍后重试" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "验证码已发送到您的邮箱",
    });

  } catch (error) {
    console.error("发送验证码错误:", error);
    return NextResponse.json(
      { error: "发送验证码失败，请稍后重试" },
      { status: 500 }
    );
  }
}

export const runtime = "nodejs";
