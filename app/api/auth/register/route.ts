import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import { generateToken } from "@/app/lib/jwt";
import { isValidEmail } from "@/app/lib/email";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { username, email, code } = body;

    // 验证输入
    if (!username || !email || !code) {
      return NextResponse.json(
        { error: "用户名、邮箱和验证码不能为空" },
        { status: 400 },
      );
    }

    // 验证邮箱格式
    if (!isValidEmail(email)) {
      return NextResponse.json({ error: "邮箱格式不正确" }, { status: 400 });
    }

    // 验证用户名格式（可选：添加用户名规则）
    if (username.length < 2 || username.length > 20) {
      return NextResponse.json({ error: "用户名长度应在2-20个字符之间" }, { status: 400 });
    }

    // 验证验证码
    const verification = await prisma.emailVerification.findFirst({
      where: {
        email,
        code,
        type: 'register',
        used: false,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!verification) {
      return NextResponse.json(
        { error: "验证码无效或已过期" },
        { status: 401 }
      );
    }

    // 检查是否允许注册
    const allowRegistration = await prisma.systemConfig.findUnique({
      where: { key: "allowUserRegistration" },
    });

    if (allowRegistration?.value === "false") {
      return NextResponse.json(
        { error: "当前不允许用户注册" },
        { status: 403 },
      );
    }

    // 检查用户名是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [{ username: username }, { email: email }],
      },
    });

    if (existingUser) {
      if (existingUser.username === username) {
        return NextResponse.json({ error: "用户名已存在" }, { status: 409 });
      } else {
        return NextResponse.json({ error: "邮箱已被注册" }, { status: 409 });
      }
    }

    // 获取默认用户角色
    const defaultRole = await prisma.systemConfig.findUnique({
      where: { key: "defaultUserRole" },
    });

    // 标记验证码为已使用
    await prisma.emailVerification.update({
      where: {
        id: verification.id,
      },
      data: {
        used: true,
      },
    });

    // 创建用户（无密码）
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: null, // 无密码注册
        role: (defaultRole?.value as "USER" | "ADMIN") || "USER",
        enabled: true,
      },
    });

    // 生成JWT token
    const token = generateToken({
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role as "USER" | "ADMIN",
    });

    // 返回用户信息和token
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt,
      },
      token,
    });

    // 设置cookie
    response.cookies.set("auth-token", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 7 * 24 * 60 * 60, // 7 days
    });

    return response;
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: "注册失败，请稍后重试" },
      { status: 500 },
    );
  }
}

export const runtime = "nodejs";
