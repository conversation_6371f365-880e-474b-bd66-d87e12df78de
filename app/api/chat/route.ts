import { NextRequest, NextResponse } from "next/server";
import { verifyUserToken } from "@/app/lib/jwt";
import {
  getProviderByType,
  isBackendConfigEnabled,
} from "@/app/lib/provider-service";
import { logEnhancedApiCall } from "@/app/lib/api-log-service";

export async function POST(req: NextRequest) {
  const startTime = Date.now();
  let providerId = "";
  let userId: string | undefined;

  try {
    // 检查是否启用后端配置
    const backendConfigEnabled = await isBackendConfigEnabled();

    if (!backendConfigEnabled) {
      return NextResponse.json(
        { error: "后端配置未启用，请使用原有API接口" },
        { status: 400 },
      );
    }

    // 验证用户身份（可选，支持匿名用户）
    const tokenData = verifyUserToken(req);
    userId = tokenData?.userId;

    console.log(
      `[Chat API] User authentication: ${
        userId ? `Authenticated as ${userId}` : "Anonymous user"
      }`,
    );
    if (tokenData) {
      console.log(
        `[Chat API] User role: ${tokenData.role}, username: ${tokenData.username}`,
      );
    } else {
      console.log(
        `[Chat API] No valid JWT token found, proceeding as anonymous user`,
      );
    }

    // 解析请求体
    const body = await req.json();
    const {
      provider: providerType,
      model,
      messages,
      stream = false,
      ...otherParams
    } = body;

    console.log(
      `[Chat API] Request: provider=${providerType}, model=${model}, stream=${stream}, messages=${
        messages?.length || 0
      }`,
    );

    // 验证必需参数
    if (!providerType || !model || !messages) {
      return NextResponse.json(
        { error: "缺少必需参数: provider, model, messages" },
        { status: 400 },
      );
    }

    // 获取服务商配置
    const providerConfig = await getProviderByType(providerType);
    if (!providerConfig) {
      return NextResponse.json(
        { error: `服务商 ${providerType} 未配置或已禁用` },
        { status: 404 },
      );
    }

    providerId = providerConfig.id;

    // 构建请求URL
    const apiUrl = `${providerConfig.baseUrl}/v1/chat/completions`;

    // 构建请求体
    const requestBody = {
      model,
      messages,
      stream,
      ...(stream && { stream_options: { include_usage: true } }), // 流式响应时包含usage信息
      ...otherParams,
      // 合并服务商特定配置
      ...(providerConfig.config || {}),
    };

    // 构建请求头
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${providerConfig.apiKey}`,
    };

    // 添加服务商特定的请求头
    if (providerConfig.config?.headers) {
      Object.assign(headers, providerConfig.config.headers);
    }

    // 发送请求到第三方API
    console.log(`[Chat API] Sending request to: ${apiUrl}`);
    const response = await fetch(apiUrl, {
      method: "POST",
      headers,
      body: JSON.stringify(requestBody),
    });

    const duration = Date.now() - startTime;
    console.log(
      `[Chat API] Response status: ${response.status}, duration: ${duration}ms`,
    );

    if (!response.ok) {
      const errorText = await response.text();

      // 记录错误日志
      await logEnhancedApiCall({
        userId,
        providerId: providerId,
        endpoint: "/v1/chat/completions",
        method: "POST",
        requestBody,
        status: "error",
        errorMsg: `HTTP ${response.status}: ${errorText}`,
        duration,
      });

      return NextResponse.json(
        { error: `API调用失败: ${response.status} ${response.statusText}` },
        { status: response.status },
      );
    }

    // 处理流式响应
    if (stream) {
      console.log(`[Chat API] Processing stream response`);

      // 创建一个转换流来捕获Token统计信息
      const transformStream = new TransformStream({
        transform(chunk, controller) {
          const text = new TextDecoder().decode(chunk);

          // 查找包含usage信息的chunk
          const lines = text.split("\n");
          for (const line of lines) {
            if (line.startsWith("data: ") && line !== "data: [DONE]") {
              try {
                const data = JSON.parse(line.slice(6));
                if (data.usage) {
                  console.log(`[Chat API] Found usage in stream:`, data.usage);

                  // 异步记录日志，不阻塞流
                  logEnhancedApiCall({
                    userId,
                    providerId: providerId,
                    endpoint: "/v1/chat/completions",
                    method: "POST",
                    requestBody,
                    responseBody: { usage: data.usage }, // 只传递usage信息
                    status: "success",
                    duration,
                  }).catch((error) => {
                    console.error(
                      "[Chat API] Failed to log stream usage:",
                      error,
                    );
                  });
                }
              } catch (e) {
                // 忽略JSON解析错误
              }
            }
          }

          controller.enqueue(chunk);
        },
      });

      // 注意：不需要fallback日志，因为TransformStream会处理所有情况

      // 返回转换后的流
      return new NextResponse(response.body?.pipeThrough(transformStream), {
        status: response.status,
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
        },
      });
    }

    // 处理普通响应
    const responseData = await response.json();

    console.log(
      `[Chat API] Response data keys: ${
        responseData ? Object.keys(responseData) : "null"
      }`,
    );
    console.log(
      `[Chat API] Response usage: ${JSON.stringify(
        responseData?.usage || "no usage",
      )}`,
    );

    // 记录成功日志（增强版，自动提取token使用量和计算成本）
    await logEnhancedApiCall({
      userId,
      providerId: providerId,
      endpoint: "/v1/chat/completions",
      method: "POST",
      requestBody,
      responseBody: responseData,
      status: "success",
      duration,
    });

    return NextResponse.json(responseData);
  } catch (error) {
    const duration = Date.now() - startTime;

    console.error("Chat API error:", error);

    // 记录错误日志
    if (providerId) {
      await logEnhancedApiCall({
        userId,
        providerId: providerId,
        endpoint: "/v1/chat/completions",
        method: "POST",
        status: "error",
        errorMsg: error instanceof Error ? error.message : "Unknown error",
        duration,
      });
    }

    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

export const runtime = "nodejs";
