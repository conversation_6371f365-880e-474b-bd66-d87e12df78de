import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";
import { hashPassword } from "@/app/lib/encryption";

// 获取用户列表
export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};
    if (search) {
      where.OR = [
        { username: { contains: search } },
        { email: { contains: search } },
      ];
    }

    // 获取用户列表和总数
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          enabled: true,
          totalTokensUsed: true,
          totalCost: true,
          monthlyTokensUsed: true,
          monthlyCost: true,
          lastResetAt: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              apiLogs: true,
              sessions: true,
            },
          },
          // 不返回密码
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get users error:", error);
    return NextResponse.json({ error: "获取用户列表失败" }, { status: 500 });
  }
}

// 创建用户
export async function POST(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const body = await req.json();
    const { username, email, password, role = "USER", enabled = true } = body;

    // 验证必需字段
    if (!username || !email || !password) {
      return NextResponse.json(
        { error: "缺少必需字段: username, email, password" },
        { status: 400 },
      );
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: "邮箱格式不正确" }, { status: 400 });
    }

    // 验证密码强度
    if (password.length < 6) {
      return NextResponse.json({ error: "密码长度至少6位" }, { status: 400 });
    }

    // 检查用户名和邮箱是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [{ username: username }, { email: email }],
      },
    });

    if (existingUser) {
      if (existingUser.username === username) {
        return NextResponse.json({ error: "用户名已存在" }, { status: 409 });
      } else {
        return NextResponse.json({ error: "邮箱已被注册" }, { status: 409 });
      }
    }

    // 创建用户
    const hashedPassword = hashPassword(password);
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        role: role as "USER" | "ADMIN",
        enabled,
      },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        enabled: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    console.log(
      `[Admin] Created user: ${user.username} (${user.email}) by ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      user,
    });
  } catch (error) {
    console.error("Create user error:", error);
    return NextResponse.json({ error: "创建用户失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
