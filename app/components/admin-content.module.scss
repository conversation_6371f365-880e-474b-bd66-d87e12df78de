.admin-content {
  flex: 1;
  height: 100%;
  background-color: var(--white);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.content-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: var(--border-in-light);
}

.content-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--black);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.content-subtitle {
  font-size: 14px;
  color: var(--gray);
  margin: 0;
  line-height: 1.4;
}

.content-body {
  flex: 1;
  min-height: 0;
}

// 占位组件样式
.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 40px;
  color: var(--gray);
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.placeholder-content h2 {
  font-size: 20px;
  font-weight: 600;
  color: var(--black);
  margin: 0 0 8px 0;
}

.placeholder-content p {
  font-size: 14px;
  color: var(--gray);
  margin: 0;
  opacity: 0.8;
}

// 响应式设计
@media (max-width: 768px) {
  .content-wrapper {
    padding: 16px;
  }
  
  .content-title {
    font-size: 20px;
  }
  
  .placeholder-content {
    padding: 24px 16px;
  }
  
  .placeholder-icon {
    font-size: 36px;
  }
}

// 暗色主题适配
.dark {
  .admin-content {
    background-color: var(--white);
  }
  
  .content-title {
    color: var(--black);
  }
  
  .content-subtitle {
    color: var(--gray);
  }
  
  .placeholder-content h2 {
    color: var(--black);
  }
  
  .placeholder-content p {
    color: var(--gray);
  }
}
