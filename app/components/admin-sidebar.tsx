import React from "react";
import { useNavigate } from "react-router-dom";
import { useAdminStore, adminMenuItems, AdminView } from "../store/admin";
import { useUserStore } from "../store/user";
import { IconButton } from "./button";
import { SideBarHeader, SideBarBody, SideBarTail } from "./sidebar";
import { AdminGuard } from "./admin-guard";
import ChatGptIcon from "../icons/chatgpt.svg";
import BackIcon from "../icons/return.svg";
import LogoutIcon from "../icons/logout.svg";
import { showConfirm } from "./ui-lib";
import { Path } from "../constant";
import styles from "./admin-sidebar.module.scss";

interface AdminSidebarProps {
  shouldNarrow: boolean;
}

export function AdminSidebar({ shouldNarrow }: AdminSidebarProps) {
  const adminStore = useAdminStore();
  const userStore = useUserStore();
  const navigate = useNavigate();

  // 视图到路径的映射
  const viewToPath: Record<AdminView, string> = {
    dashboard: Path.AdminDashboard,
    providers: Path.AdminProviders,
    users: Path.AdminUsers,
    logs: Path.AdminLogs,
    settings: Path.AdminSettings,
  };

  const handleMenuClick = (view: AdminView) => {
    adminStore.setCurrentView(view);
    // 同时更新URL路径
    navigate(viewToPath[view]);
  };

  const handleBackToChat = () => {
    adminStore.exitAdminMode();
    navigate(Path.Home);
  };

  const handleLogout = () => {
    showConfirm({
      title: "确认登出",
      content: "您确定要登出管理后台吗？",
      onConfirm: async () => {
        await userStore.logout();
        adminStore.exitAdminMode();
      },
    });
  };

  return (
    <AdminGuard>
      <SideBarHeader
        title="QADChat"
        subTitle="管理后台"
        logo={<ChatGptIcon />}
        shouldNarrow={shouldNarrow}
      >
        <div className={styles["admin-user-info"]}>
          <div className={styles["admin-user-name"]}>
            {userStore.user?.username}
          </div>
          <div className={styles["admin-user-role"]}>
            {userStore.user?.role === "ADMIN" ? "管理员" : "用户"}
          </div>
        </div>
      </SideBarHeader>

      <SideBarBody>
        <div className={styles["admin-menu"]}>
          {adminMenuItems.map((item) => (
            <div
              key={item.id}
              className={`${styles["menu-item"]} ${
                adminStore.currentView === item.id ? styles["active"] : ""
              }`}
              onClick={() => handleMenuClick(item.id)}
            >
              <div className={styles["menu-icon"]}>{item.icon}</div>
              {!shouldNarrow && (
                <div className={styles["menu-content"]}>
                  <div className={styles["menu-name"]}>{item.name}</div>
                  {item.description && (
                    <div className={styles["menu-description"]}>
                      {item.description}
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </SideBarBody>

      <SideBarTail
        primaryAction={
          <IconButton
            icon={<BackIcon />}
            text={shouldNarrow ? undefined : "返回聊天"}
            onClick={handleBackToChat}
            className={styles["footer-button"]}
          />
        }
        secondaryAction={
          <IconButton
            icon={<LogoutIcon />}
            text={shouldNarrow ? undefined : "登出"}
            onClick={handleLogout}
            className={`${styles["footer-button"]} ${styles["logout-button"]}`}
          />
        }
      />
    </AdminGuard>
  );
}
