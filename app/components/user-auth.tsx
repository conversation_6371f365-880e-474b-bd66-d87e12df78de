import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Path } from "../constant";
import { useUserStore } from "../store/user";
import { useAppConfig, Theme } from "../store/config";
import LeftIcon from "@/app/icons/left.svg";
import LightIcon from "../icons/light.svg";
import DarkIcon from "../icons/dark.svg";
import AutoIcon from "../icons/auto.svg";

type AuthMode = "login" | "register";

export function UserAuthPage() {
  const navigate = useNavigate();
  const userStore = useUserStore();
  const config = useAppConfig();
  const [mode, setMode] = useState<AuthMode>("login");
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    code: "",
  });
  const [sendingCode, setSendingCode] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);

  const theme = config.theme;

  // 如果已经登录，直接跳转到聊天页面
  useEffect(() => {
    if (userStore.isAuthenticated) {
      navigate(Path.Chat);
    }
  }, [userStore.isAuthenticated, navigate]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async () => {
    if (mode === "login") {
      if (!formData.email || !formData.code) {
        userStore.setError("请填写邮箱和验证码");
        return;
      }

      const success = await userStore.loginWithCode(
        formData.email,
        formData.code,
      );
      if (success) {
        navigate(Path.Chat);
      }
    } else {
      // 注册模式
      if (!formData.username || !formData.email || !formData.code) {
        userStore.setError("请填写所有必填字段");
        return;
      }

      const success = await userStore.registerWithCode(
        formData.username,
        formData.email,
        formData.code,
      );
      if (success) {
        navigate(Path.Chat);
      }
    }
  };

  // 发送验证码
  const handleSendCode = async () => {
    if (!formData.email) {
      userStore.setError("请先填写邮箱地址");
      return;
    }

    setSendingCode(true);
    const success = await userStore.sendVerificationCode(formData.email, mode);
    setSendingCode(false);

    if (success) {
      setCodeSent(true);
      setCountdown(60);
      
      // 开始倒计时
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setCodeSent(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
  };

  const switchMode = () => {
    setMode(mode === "login" ? "register" : "login");
    setFormData({
      username: "",
      email: "",
      code: "",
    });
    setCodeSent(false);
    setCountdown(0);
    userStore.setError(null);
  };

  const getThemeIcon = () => {
    switch (theme) {
      case "light":
        return <LightIcon style={{ width: "14px", height: "14px" }} />;
      case "dark":
        return <DarkIcon style={{ width: "14px", height: "14px" }} />;
      default:
        return <AutoIcon style={{ width: "14px", height: "14px" }} />;
    }
  };

  const nextTheme = () => {
    const themes: Theme[] = ["auto", "light", "dark"];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    config.update((config) => (config.theme = themes[nextIndex]));
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        background:
          theme === "light"
            ? "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)"
            : "linear-gradient(135deg, #0f172a 0%, #1e293b 100%)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: "16px",
        position: "relative",
        fontFamily: "Inter, 'Noto Sans SC', -apple-system, BlinkMacSystemFont, sans-serif",
      }}
    >
      {/* 主题切换按钮 */}
      <button
        onClick={nextTheme}
        style={{
          position: "fixed",
          top: "24px",
          right: "24px",
          width: "40px",
          height: "40px",
          borderRadius: "50%",
          background: theme === "light" ? "#ffffff" : "#1e293b",
          border: theme === "light" ? "1px solid #e2e8f0" : "1px solid #475569",
          boxShadow: theme === "light" ? "0 4px 6px -1px rgba(0, 0, 0, 0.1)" : "0 4px 6px -1px rgba(0, 0, 0, 0.3)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          transition: "all 0.2s ease",
          zIndex: 10,
          color: theme === "light" ? "#64748b" : "#cbd5e1",
        }}
        title={`当前主题: ${theme === "auto" ? "自动" : theme === "light" ? "浅色" : "深色"}`}
      >
        {getThemeIcon()}
      </button>

      {/* 返回按钮 */}
      <button
        onClick={() => navigate(Path.Home)}
        style={{
          position: "fixed",
          top: "24px",
          left: "24px",
          width: "40px",
          height: "40px",
          borderRadius: "50%",
          background: theme === "light" ? "#ffffff" : "#1e293b",
          border: theme === "light" ? "1px solid #e2e8f0" : "1px solid #475569",
          boxShadow: theme === "light" ? "0 4px 6px -1px rgba(0, 0, 0, 0.1)" : "0 4px 6px -1px rgba(0, 0, 0, 0.3)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          transition: "all 0.2s ease",
          zIndex: 10,
          color: theme === "light" ? "#64748b" : "#cbd5e1",
        }}
      >
        <LeftIcon style={{ width: "14px", height: "14px" }} />
      </button>

      {/* 登录卡片 */}
      <div
        style={{
          width: "100%",
          maxWidth: "400px",
          background: theme === "light" ? "#ffffff" : "#1e293b",
          border: theme === "light" ? "1px solid #e2e8f0" : "1px solid #475569",
          borderRadius: "16px",
          boxShadow: theme === "light" 
            ? "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -1px rgba(0, 0, 0, 0.05)"
            : "0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -1px rgba(0, 0, 0, 0.3)",
          padding: "32px",
          backdropFilter: "blur(10px)",
        }}
      >
        {/* 品牌展示区域 */}
        <div style={{ textAlign: "center", marginBottom: "32px" }}>
          {/* Logo区域 */}
          <div style={{ display: "flex", alignItems: "center", justifyContent: "center", marginBottom: "24px" }}>
            <div
              style={{
                width: "48px",
                height: "48px",
                borderRadius: "16px",
                background: "linear-gradient(135deg, #3b82f6, #2563eb)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginRight: "12px",
                boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
              }}
            >
              <span style={{ fontSize: "20px", color: "white" }}>💬</span>
            </div>
            <h1
              style={{
                fontSize: "24px",
                fontWeight: "bold",
                background: "linear-gradient(135deg, #3b82f6, #2563eb)",
                backgroundClip: "text",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                margin: 0,
              }}
            >
              NextChat
            </h1>
          </div>

          {/* 问候语 */}
          <div>
            <h2 style={{ 
              fontSize: "20px", 
              fontWeight: "600", 
              marginBottom: "8px", 
              color: theme === "light" ? "#1e293b" : "#f1f5f9",
              margin: "0 0 8px 0"
            }}>
              Hi, I&apos;m NextChat
            </h2>
            <p style={{ 
              fontSize: "14px", 
              color: theme === "light" ? "#64748b" : "#cbd5e1",
              margin: 0
            }}>
              AI At Model No Subscription No Tricks
            </p>
          </div>
        </div>

        {/* 表单区域 */}
        <div style={{ marginBottom: "24px" }}>
          {/* 注册时显示用户名输入 */}
          {mode === "register" && (
            <div style={{ marginBottom: "20px" }}>
              <label style={{
                display: "block",
                fontSize: "14px",
                fontWeight: "500",
                marginBottom: "8px",
                color: theme === "light" ? "#64748b" : "#cbd5e1"
              }}>
                用户名
              </label>
              <input
                type="text"
                placeholder="Enter Username"
                value={formData.username}
                onChange={(e) => handleInputChange("username", e.target.value)}
                style={{
                  width: "100%",
                  padding: "12px 16px",
                  borderRadius: "12px",
                  border: theme === "light" ? "1px solid #e2e8f0" : "1px solid #475569",
                  background: theme === "light" ? "#f8fafc" : "#334155",
                  color: theme === "light" ? "#1e293b" : "#f1f5f9",
                  fontSize: "16px",
                  transition: "all 0.2s ease",
                  outline: "none",
                  boxSizing: "border-box",
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = "#3b82f6";
                  e.target.style.background = theme === "light" ? "#ffffff" : "#1e293b";
                  e.target.style.boxShadow = "0 0 0 3px rgba(59, 130, 246, 0.1)";
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = theme === "light" ? "#e2e8f0" : "#475569";
                  e.target.style.background = theme === "light" ? "#f8fafc" : "#334155";
                  e.target.style.boxShadow = "none";
                }}
              />
            </div>
          )}

          {/* 邮箱输入 */}
          <div style={{ marginBottom: "20px" }}>
            <label style={{
              display: "block",
              fontSize: "14px",
              fontWeight: "500",
              marginBottom: "8px",
              color: theme === "light" ? "#64748b" : "#cbd5e1"
            }}>
              邮箱地址
            </label>
            <input
              type="email"
              placeholder="Enter Email"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              style={{
                width: "100%",
                padding: "12px 16px",
                borderRadius: "12px",
                border: theme === "light" ? "1px solid #e2e8f0" : "1px solid #475569",
                background: theme === "light" ? "#f8fafc" : "#334155",
                color: theme === "light" ? "#1e293b" : "#f1f5f9",
                fontSize: "16px",
                transition: "all 0.2s ease",
                outline: "none",
                boxSizing: "border-box",
              }}
              onFocus={(e) => {
                e.target.style.borderColor = "#3b82f6";
                e.target.style.background = theme === "light" ? "#ffffff" : "#1e293b";
                e.target.style.boxShadow = "0 0 0 3px rgba(59, 130, 246, 0.1)";
              }}
              onBlur={(e) => {
                e.target.style.borderColor = theme === "light" ? "#e2e8f0" : "#475569";
                e.target.style.background = theme === "light" ? "#f8fafc" : "#334155";
                e.target.style.boxShadow = "none";
              }}
            />
          </div>

          {/* 验证码输入 */}
          <div style={{ display: "grid", gridTemplateColumns: "1fr auto", gap: "12px", marginBottom: "24px" }}>
            <div>
              <label style={{
                display: "block",
                fontSize: "14px",
                fontWeight: "500",
                marginBottom: "8px",
                color: theme === "light" ? "#64748b" : "#cbd5e1"
              }}>
                验证码
              </label>
              <input
                type="text"
                placeholder="6-digit Code"
                value={formData.code}
                onChange={(e) => handleInputChange("code", e.target.value)}
                style={{
                  width: "100%",
                  padding: "12px 16px",
                  borderRadius: "12px",
                  border: theme === "light" ? "1px solid #e2e8f0" : "1px solid #475569",
                  background: theme === "light" ? "#f8fafc" : "#334155",
                  color: theme === "light" ? "#1e293b" : "#f1f5f9",
                  fontSize: "16px",
                  transition: "all 0.2s ease",
                  outline: "none",
                  boxSizing: "border-box",
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = "#3b82f6";
                  e.target.style.background = theme === "light" ? "#ffffff" : "#1e293b";
                  e.target.style.boxShadow = "0 0 0 3px rgba(59, 130, 246, 0.1)";
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = theme === "light" ? "#e2e8f0" : "#475569";
                  e.target.style.background = theme === "light" ? "#f8fafc" : "#334155";
                  e.target.style.boxShadow = "none";
                }}
              />
            </div>
            <div style={{ display: "flex", alignItems: "end" }}>
              <button
                type="button"
                onClick={handleSendCode}
                disabled={sendingCode || codeSent || !formData.email}
                style={{
                  padding: "12px 16px",
                  borderRadius: "12px",
                  border: theme === "light" ? "1px solid #e2e8f0" : "1px solid #475569",
                  background: theme === "light" ? "#ffffff" : "#1e293b",
                  color: theme === "light" ? "#1e293b" : "#f1f5f9",
                  fontSize: "14px",
                  fontWeight: "500",
                  cursor: sendingCode || codeSent || !formData.email ? "not-allowed" : "pointer",
                  transition: "all 0.2s ease",
                  opacity: sendingCode || codeSent || !formData.email ? 0.6 : 1,
                  whiteSpace: "nowrap",
                }}
              >
                {sendingCode ? "Sending..." : codeSent ? `${countdown}s` : "Send Code"}
              </button>
            </div>
          </div>

          {/* 登录/注册按钮 */}
          <button
            onClick={handleSubmit}
            disabled={userStore.isLoading}
            style={{
              width: "100%",
              padding: "12px",
              borderRadius: "12px",
              border: "none",
              background: "#3b82f6",
              color: "white",
              fontSize: "16px",
              fontWeight: "500",
              cursor: userStore.isLoading ? "not-allowed" : "pointer",
              transition: "all 0.2s ease",
              opacity: userStore.isLoading ? 0.7 : 1,
            }}
            onMouseEnter={(e) => {
              if (!userStore.isLoading) {
                e.currentTarget.style.background = "#2563eb";
                e.currentTarget.style.transform = "translateY(-1px)";
                e.currentTarget.style.boxShadow = "0 10px 15px -3px rgba(0, 0, 0, 0.1)";
              }
            }}
            onMouseLeave={(e) => {
              if (!userStore.isLoading) {
                e.currentTarget.style.background = "#3b82f6";
                e.currentTarget.style.transform = "translateY(0)";
                e.currentTarget.style.boxShadow = "none";
              }
            }}
          >
            {userStore.isLoading ? "处理中..." : mode === "login" ? "Login" : "Register"}
          </button>
        </div>

        {/* 切换模式 */}
        <div style={{ textAlign: "center", marginBottom: "16px" }}>
          <p style={{
            fontSize: "14px",
            color: theme === "light" ? "#64748b" : "#cbd5e1",
            margin: 0
          }}>
            {mode === "login" ? "没有账户？" : "已有账户？"}
            <button
              type="button"
              onClick={switchMode}
              style={{
                background: "none",
                border: "none",
                color: "#3b82f6",
                fontSize: "14px",
                fontWeight: "500",
                cursor: "pointer",
                marginLeft: "4px",
                textDecoration: "none",
                transition: "opacity 0.2s ease",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = "0.8";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = "1";
              }}
            >
              {mode === "login" ? "立即注册" : "立即登录"}
            </button>
          </p>
        </div>

        {/* 隐私政策 */}
        <p style={{
          textAlign: "center",
          fontSize: "12px",
          color: theme === "light" ? "#94a3b8" : "#94a3b8",
          margin: 0
        }}>
          By continuing, you agree to{" "}
          <a
            href="#"
            style={{
              color: "#3b82f6",
              textDecoration: "none",
              transition: "opacity 0.2s ease"
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = "0.8";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = "1";
            }}
          >
            Privacy Policy
          </a>
        </p>

        {/* 错误信息显示 */}
        {userStore.error && (
          <div style={{
            marginTop: "16px",
            padding: "12px",
            borderRadius: "8px",
            background: "#fef2f2",
            border: "1px solid #fecaca",
            color: "#dc2626",
            fontSize: "14px",
            textAlign: "center",
          }}>
            {userStore.error}
          </div>
        )}
      </div>
    </div>
  );
}
