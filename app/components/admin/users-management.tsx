"use client";

import React, { useState, useEffect } from "react";
import styles from "./users-management.module.scss";

interface User {
  id: string;
  username: string;
  email: string;
  role: "USER" | "ADMIN";
  enabled: boolean;
  totalTokensUsed: number;
  totalCost: number;
  monthlyTokensUsed: number;
  monthlyCost: number;
  lastResetAt: string;
  createdAt: string;
  updatedAt: string;
  _count: {
    apiLogs: number;
    sessions: number;
  };
}

interface UsersResponse {
  users: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export function UsersManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });
  const [search, setSearch] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [enabledFilter, setEnabledFilter] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search,
        role: roleFilter,
        enabled: enabledFilter,
      });

      const response = await fetch(`/api/admin/users?${params}`);
      if (!response.ok) {
        throw new Error("获取用户列表失败");
      }

      const data: UsersResponse = await response.json();
      setUsers(data.users);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取用户列表失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [pagination.page, search, roleFilter, enabledFilter]);

  // 切换用户状态
  const toggleUserStatus = async (userId: string, enabled: boolean) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ enabled: !enabled }),
      });

      if (!response.ok) {
        throw new Error("更新用户状态失败");
      }

      await fetchUsers();
    } catch (err) {
      setError(err instanceof Error ? err.message : "更新用户状态失败");
    }
  };

  // 重置用户月度统计
  const resetUserStats = async (userId: string) => {
    if (!confirm("确定要重置该用户的月度统计吗？")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/users/${userId}/reset-stats`, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("重置统计失败");
      }

      await fetchUsers();
    } catch (err) {
      setError(err instanceof Error ? err.message : "重置统计失败");
    }
  };

  // 删除用户
  const deleteUser = async (userId: string) => {
    if (!confirm("确定要删除该用户吗？此操作不可恢复！")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("删除用户失败");
      }

      await fetchUsers();
    } catch (err) {
      setError(err instanceof Error ? err.message : "删除用户失败");
    }
  };

  // 格式化数字
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // 格式化成本
  const formatCost = (cost: number) => {
    return `$${cost.toFixed(6)}`;
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  if (loading) {
    return <div className={styles.loading}>加载中...</div>;
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>用户管理</h2>
        <button
          className={styles.createButton}
          onClick={() => setShowCreateModal(true)}
        >
          添加用户
        </button>
      </div>

      {error && <div className={styles.error}>{error}</div>}

      {/* 筛选器 */}
      <div className={styles.filters}>
        <input
          type="text"
          placeholder="搜索用户名或邮箱..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className={styles.searchInput}
        />
        <select
          value={roleFilter}
          onChange={(e) => setRoleFilter(e.target.value)}
          className={styles.filterSelect}
        >
          <option value="">所有角色</option>
          <option value="USER">普通用户</option>
          <option value="ADMIN">管理员</option>
        </select>
        <select
          value={enabledFilter}
          onChange={(e) => setEnabledFilter(e.target.value)}
          className={styles.filterSelect}
        >
          <option value="">所有状态</option>
          <option value="true">已启用</option>
          <option value="false">已禁用</option>
        </select>
      </div>

      {/* 用户表格 */}
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>用户名</th>
              <th>邮箱</th>
              <th>角色</th>
              <th>状态</th>
              <th>总Token</th>
              <th>总成本</th>
              <th>月度Token</th>
              <th>月度成本</th>
              <th>API调用</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr key={user.id}>
                <td>{user.username}</td>
                <td>{user.email}</td>
                <td>
                  <span
                    className={`${styles.role} ${
                      user.role === "ADMIN" ? styles.admin : styles.user
                    }`}
                  >
                    {user.role === "ADMIN" ? "管理员" : "普通用户"}
                  </span>
                </td>
                <td>
                  <span
                    className={`${styles.status} ${
                      user.enabled ? styles.enabled : styles.disabled
                    }`}
                  >
                    {user.enabled ? "已启用" : "已禁用"}
                  </span>
                </td>
                <td>{formatNumber(user.totalTokensUsed)}</td>
                <td>{formatCost(user.totalCost)}</td>
                <td>{formatNumber(user.monthlyTokensUsed)}</td>
                <td>{formatCost(user.monthlyCost)}</td>
                <td>{formatNumber(user._count.apiLogs)}</td>
                <td>{formatDate(user.createdAt)}</td>
                <td>
                  <div className={styles.actions}>
                    <button
                      onClick={() => setSelectedUser(user)}
                      className={styles.viewButton}
                      title="查看详情"
                    >
                      详情
                    </button>
                    <button
                      onClick={() => toggleUserStatus(user.id, user.enabled)}
                      className={
                        user.enabled
                          ? styles.disableButton
                          : styles.enableButton
                      }
                      title={user.enabled ? "禁用用户" : "启用用户"}
                    >
                      {user.enabled ? "禁用" : "启用"}
                    </button>
                    <button
                      onClick={() => resetUserStats(user.id)}
                      className={styles.resetButton}
                      title="重置月度统计"
                    >
                      重置
                    </button>
                    {user.role !== "ADMIN" && (
                      <button
                        onClick={() => deleteUser(user.id)}
                        className={styles.deleteButton}
                        title="删除用户"
                      >
                        删除
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 分页 */}
      <div className={styles.pagination}>
        <button
          onClick={() =>
            setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
          }
          disabled={pagination.page <= 1}
          className={styles.pageButton}
        >
          上一页
        </button>
        <span className={styles.pageInfo}>
          第 {pagination.page} 页，共 {pagination.pages} 页
        </span>
        <button
          onClick={() =>
            setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
          }
          disabled={pagination.page >= pagination.pages}
          className={styles.pageButton}
        >
          下一页
        </button>
      </div>
    </div>
  );
}
