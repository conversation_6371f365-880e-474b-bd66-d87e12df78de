import { useState, useEffect } from "react";
import { useUserStore } from "@/app/store/user";
import { Card } from "../ui-lib";
import styles from "./admin-dashboard.module.scss";

interface DashboardStats {
  totalUsers: number;
  totalProviders: number;
  totalApiCalls: number;
  successRate: string;
  totalTokens: number;
  totalCost: number;
  activeUsers: number;
  totalRevenue: number;
  averageRevenuePerUser: number;
}

interface RecentActivity {
  id: string;
  type: "api_call" | "user_register" | "provider_add";
  message: string;
  time: string;
  status: "success" | "error" | "info";
}

interface TopModel {
  model: string;
  calls: number;
  tokens: number;
  cost: number;
}

interface DailyTrend {
  date: string;
  calls: number;
  successCalls: number;
  successRate: string;
}

interface BillingStats {
  totalRevenue: number;
  totalUsers: number;
  activeUsers: number;
  averageRevenuePerUser: number;
  planDistribution: Array<{
    planId: string;
    userCount: number;
    revenue: number;
  }>;
}

export function AdminDashboard() {
  const userStore = useUserStore();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalProviders: 0,
    totalApiCalls: 0,
    successRate: "0%",
    totalTokens: 0,
    totalCost: 0,
    activeUsers: 0,
    totalRevenue: 0,
    averageRevenuePerUser: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [topModels, setTopModels] = useState<TopModel[]>([]);
  const [dailyTrend, setDailyTrend] = useState<DailyTrend[]>([]);
  const [billingStats, setBillingStats] = useState<BillingStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // 并行获取统计数据和计费数据
      const [statsResponse, billingResponse] = await Promise.all([
        fetch("/api/admin/stats?days=30", {
          headers: {
            Authorization: `Bearer ${userStore.token}`,
          },
        }),
        fetch("/api/admin/billing?days=30", {
          headers: {
            Authorization: `Bearer ${userStore.token}`,
          },
        }),
      ]);

      const [statsData, billingData] = await Promise.all([
        statsResponse.json(),
        billingResponse.json(),
      ]);

      if (statsData.success) {
        const { overview, topProviders, topUsers, topModels, dailyTrend } =
          statsData.stats;
        setStats({
          totalUsers: overview.totalUsers,
          totalProviders: overview.totalProviders,
          totalApiCalls: overview.totalCalls,
          successRate: overview.successRate + "%",
          totalTokens: overview.totalTokens,
          totalCost: overview.totalCost, // 来自apiStats，基于API日志的成本统计
          activeUsers: overview.recentUsers || 0,
          totalRevenue: overview.totalCost || 0,
          averageRevenuePerUser:
            overview.totalUsers > 0
              ? (overview.totalCost || 0) / overview.totalUsers
              : 0,
        });

        // 设置最活跃的模型数据
        const modelData: TopModel[] = (topModels || [])
          .slice(0, 5)
          .map((model: any) => ({
            model: model.model,
            calls: model._count.id,
            tokens: model._sum.tokensUsed || 0,
            cost: model._sum.cost || 0,
          }));
        setTopModels(modelData);

        // 设置每日趋势数据
        setDailyTrend(dailyTrend || []);

        // 生成最近活动（基于真实数据的模拟）
        const activities: RecentActivity[] = [];

        if (overview.recentUsers > 0) {
          activities.push({
            id: "recent-users",
            type: "user_register",
            message: `最近30天新增 ${overview.recentUsers} 个用户`,
            time: "今天",
            status: "info",
          });
        }

        if (overview.totalCalls > 0) {
          activities.push({
            id: "api-calls",
            type: "api_call",
            message: `今日API调用 ${Math.floor(overview.totalCalls / 30)} 次`,
            time: "今天",
            status: "success",
          });
        }

        if (topProviders.length > 0) {
          const topProvider = topProviders[0];
          activities.push({
            id: "top-provider",
            type: "provider_add",
            message: `${topProvider.provider.name} 是最活跃的服务商`,
            time: "统计数据",
            status: "info",
          });
        }

        if (parseFloat(overview.successRate) < 95) {
          activities.push({
            id: "low-success",
            type: "api_call",
            message: `API成功率较低: ${overview.successRate}%`,
            time: "需要关注",
            status: "error",
          });
        }

        setRecentActivity(activities);

        console.log("[Dashboard] Loaded real stats:", overview);
      } else {
        console.error("Failed to load stats:", statsData.error);
        // 使用默认数据
        setStats({
          totalUsers: 0,
          totalProviders: 0,
          totalApiCalls: 0,
          successRate: "0%",
          totalTokens: 0,
          totalCost: 0,
          activeUsers: 0,
          totalRevenue: 0,
          averageRevenuePerUser: 0,
        });
        setRecentActivity([]);
        setTopModels([]);
        setDailyTrend([]);
      }

      if (billingData.success) {
        setBillingStats(billingData.data.stats);
      }
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
      // 使用默认数据
      setStats({
        totalUsers: 0,
        totalProviders: 0,
        totalApiCalls: 0,
        successRate: "0%",
        totalTokens: 0,
        totalCost: 0,
        activeUsers: 0,
        totalRevenue: 0,
        averageRevenuePerUser: 0,
      });
      setRecentActivity([]);
      setTopModels([]);
      setDailyTrend([]);
      setBillingStats(null);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: RecentActivity["type"]) => {
    switch (type) {
      case "api_call":
        return "🔄";
      case "user_register":
        return "👤";
      case "provider_add":
        return "⚙️";
      default:
        return "📝";
    }
  };

  const getActivityColor = (status: RecentActivity["status"]) => {
    switch (status) {
      case "success":
        return "#52c41a";
      case "error":
        return "#ff4d4f";
      case "info":
        return "#1890ff";
      default:
        return "#666";
    }
  };

  if (loading) {
    return (
      <div className={styles["dashboard-loading"]}>
        <div>正在加载仪表盘数据...</div>
      </div>
    );
  }

  return (
    <div className={styles["admin-dashboard"]}>
      {/* 页面标题 */}
      <div className={styles["dashboard-header"]}>
        <h1>仪表盘</h1>
        <p>系统概览和实时统计</p>
      </div>

      {/* 统计卡片 */}
      <div className={styles["stats-grid"]}>
        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>👥</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>{stats.totalUsers}</div>
            <div className={styles["stat-label"]}>总用户数</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>⚙️</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>{stats.totalProviders}</div>
            <div className={styles["stat-label"]}>服务商数量</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>📊</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>
              {stats.totalApiCalls.toLocaleString()}
            </div>
            <div className={styles["stat-label"]}>API调用次数</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>✅</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>{stats.successRate}</div>
            <div className={styles["stat-label"]}>成功率</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>🎯</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>
              {stats.totalTokens.toLocaleString()}
            </div>
            <div className={styles["stat-label"]}>Token使用量</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>💰</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>
              ${stats.totalCost.toFixed(2)}
            </div>
            <div className={styles["stat-label"]}>总成本</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>🔥</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>{stats.activeUsers}</div>
            <div className={styles["stat-label"]}>活跃用户</div>
          </div>
        </Card>

        <Card className={styles["stat-card"]}>
          <div className={styles["stat-icon"]}>💵</div>
          <div className={styles["stat-content"]}>
            <div className={styles["stat-value"]}>
              ${stats.averageRevenuePerUser.toFixed(2)}
            </div>
            <div className={styles["stat-label"]}>人均收入</div>
          </div>
        </Card>
      </div>

      {/* 数据图表区域 */}
      <div className={styles["charts-section"]}>
        {/* 最活跃模型 */}
        <Card className={styles["chart-card"]}>
          <div className={styles["chart-header"]}>
            <h3>最活跃模型</h3>
          </div>
          <div className={styles["models-list"]}>
            {topModels.length > 0 ? (
              topModels.map((model, index) => (
                <div key={model.model} className={styles["model-item"]}>
                  <div className={styles["model-rank"]}>#{index + 1}</div>
                  <div className={styles["model-info"]}>
                    <div className={styles["model-name"]}>{model.model}</div>
                    <div className={styles["model-stats"]}>
                      {model.calls} 次调用
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className={styles["no-data"]}>暂无数据</div>
            )}
          </div>
        </Card>

        {/* 每日趋势 */}
        <Card className={styles["chart-card"]}>
          <div className={styles["chart-header"]}>
            <h3>7日趋势</h3>
          </div>
          <div className={styles["trend-chart"]}>
            {dailyTrend.length > 0 ? (
              <div className={styles["trend-list"]}>
                {dailyTrend.slice(-7).map((day) => (
                  <div key={day.date} className={styles["trend-item"]}>
                    <div className={styles["trend-date"]}>
                      {new Date(day.date).toLocaleDateString("zh-CN", {
                        month: "short",
                        day: "numeric",
                      })}
                    </div>
                    <div className={styles["trend-bar"]}>
                      <div
                        className={styles["trend-fill"]}
                        style={{
                          width: `${Math.min(
                            100,
                            (day.calls /
                              Math.max(...dailyTrend.map((d) => d.calls))) *
                              100,
                          )}%`,
                        }}
                      ></div>
                    </div>
                    <div className={styles["trend-value"]}>{day.calls}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={styles["no-data"]}>暂无数据</div>
            )}
          </div>
        </Card>

        {/* 计费统计 */}
        {billingStats && (
          <Card className={styles["chart-card"]}>
            <div className={styles["chart-header"]}>
              <h3>计费统计</h3>
            </div>
            <div className={styles["billing-stats"]}>
              <div className={styles["billing-item"]}>
                <div className={styles["billing-label"]}>总收入</div>
                <div className={styles["billing-value"]}>
                  ${billingStats.totalRevenue.toFixed(2)}
                </div>
              </div>
              <div className={styles["billing-item"]}>
                <div className={styles["billing-label"]}>活跃用户</div>
                <div className={styles["billing-value"]}>
                  {billingStats.activeUsers}
                </div>
              </div>
              <div className={styles["billing-item"]}>
                <div className={styles["billing-label"]}>人均收入</div>
                <div className={styles["billing-value"]}>
                  ${billingStats.averageRevenuePerUser.toFixed(2)}
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>

      {/* 最近活动 */}
      <div className={styles["recent-activity"]}>
        <Card>
          <div className={styles["activity-header"]}>
            <h3>最近活动</h3>
            <button
              onClick={loadDashboardData}
              className={styles["refresh-button"]}
            >
              刷新
            </button>
          </div>
          <div className={styles["activity-list"]}>
            {recentActivity.map((activity) => (
              <div key={activity.id} className={styles["activity-item"]}>
                <div className={styles["activity-icon"]}>
                  {getActivityIcon(activity.type)}
                </div>
                <div className={styles["activity-content"]}>
                  <div className={styles["activity-message"]}>
                    {activity.message}
                  </div>
                  <div className={styles["activity-time"]}>{activity.time}</div>
                </div>
                <div
                  className={styles["activity-status"]}
                  style={{ color: getActivityColor(activity.status) }}
                >
                  ●
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}
