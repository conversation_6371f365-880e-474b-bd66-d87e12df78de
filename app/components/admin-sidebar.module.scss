// 管理侧边栏样式 - 现在使用标准的 SideBar 布局组件

.admin-user-info {
  padding: 12px 16px;
  background-color: var(--hover-color);
  border-radius: 8px;
  margin-bottom: 16px;
  
  .admin-user-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 4px;
  }
  
  .admin-user-role {
    font-size: 12px;
    color: var(--primary);
    background-color: rgba(29, 147, 171, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
  }
}

.admin-menu {
  padding: 8px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 4px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  
  &:hover {
    background-color: var(--hover-color);
  }
  
  &.active {
    background-color: var(--primary);
    color: white;
    
    .menu-icon {
      filter: brightness(1.2);
    }
    
    .menu-name {
      color: white;
      font-weight: 600;
    }
    
    .menu-description {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.menu-icon {
  font-size: 18px;
  margin-right: 12px;
  min-width: 24px;
  text-align: center;
}

.menu-content {
  flex: 1;
  min-width: 0;
}

.menu-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 2px;
  line-height: 1.2;
}

.menu-description {
  font-size: 12px;
  color: var(--gray);
  line-height: 1.3;
  opacity: 0.8;
}

.footer-button {
  justify-content: flex-start;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;

  &.logout-button {
    color: #dc3545;

    &:hover {
      background-color: rgba(220, 53, 69, 0.1);
    }
  }
}

// 窄侧边栏样式 - 现在由标准 SideBar 组件处理

// 暗色主题适配 - 现在由标准 SideBar 组件处理
.dark {
  .admin-user-info {
    background-color: var(--hover-color);
  }

  .menu-item {
    &:hover {
      background-color: var(--hover-color);
    }

    &.active {
      background-color: var(--primary);
    }
  }

  .menu-name {
    color: var(--black);
  }

  .menu-description {
    color: var(--gray);
  }
}
