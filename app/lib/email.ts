import nodemailer from 'nodemailer';

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

// 默认邮件配置（可以从环境变量或数据库配置中获取）
const getEmailConfig = (): EmailConfig => {
  return {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || '',
      pass: process.env.SMTP_PASS || '',
    },
  };
};

// 创建邮件传输器
const createTransporter = () => {
  const config = getEmailConfig();
  
  if (!config.auth.user || !config.auth.pass) {
    throw new Error('SMTP配置不完整，请检查环境变量');
  }
  
  return nodemailer.createTransporter(config);
};

// 发送验证码邮件
export const sendVerificationCode = async (
  email: string,
  code: string,
  type: 'login' | 'register'
) => {
  try {
    const transporter = createTransporter();
    
    const subject = type === 'login' ? 'QADChat 登录验证码' : 'QADChat 注册验证码';
    const action = type === 'login' ? '登录' : '注册';
    
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .container {
            background: #ffffff;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            font-size: 28px;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 10px;
          }
          .code-container {
            background: #f8fafc;
            border: 2px dashed #3b82f6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 30px 0;
          }
          .code {
            font-size: 32px;
            font-weight: bold;
            color: #3b82f6;
            letter-spacing: 4px;
            font-family: 'Courier New', monospace;
          }
          .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            font-size: 14px;
            color: #64748b;
            text-align: center;
          }
          .warning {
            background: #fef3cd;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 12px;
            margin: 20px 0;
            font-size: 14px;
            color: #92400e;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🤖 QADChat</div>
            <h1>邮箱验证码</h1>
            <p>您正在进行${action}操作，请使用以下验证码完成验证</p>
          </div>
          
          <div class="code-container">
            <div class="code">${code}</div>
          </div>
          
          <div class="warning">
            ⚠️ 验证码有效期为5分钟，请及时使用。如果不是您本人操作，请忽略此邮件。
          </div>
          
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
            <p>© 2024 QADChat. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
    
    const textContent = `
      QADChat ${action}验证码
      
      您的验证码是：${code}
      
      验证码有效期为5分钟，请及时使用。
      如果不是您本人操作，请忽略此邮件。
      
      © 2024 QADChat
    `;
    
    const mailOptions = {
      from: `"QADChat" <${getEmailConfig().auth.user}>`,
      to: email,
      subject,
      text: textContent,
      html: htmlContent,
    };
    
    const result = await transporter.sendMail(mailOptions);
    console.log('邮件发送成功:', result.messageId);
    return true;
  } catch (error) {
    console.error('邮件发送失败:', error);
    throw new Error('邮件发送失败');
  }
};

// 生成6位数字验证码
export const generateVerificationCode = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 验证邮箱格式
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
