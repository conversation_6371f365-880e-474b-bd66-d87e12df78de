import { prisma } from "./prisma";
import { MODEL_PRICING, calculateCost } from "./api-log-service";

export interface UserUsageStats {
  userId: string;
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  totalTokens: number;
  promptTokens: number;
  completionTokens: number;
  totalCost: number;
  successRate: number;
  averageResponseTime: number;
  topModels: Array<{
    model: string;
    calls: number;
    tokens: number;
    cost: number;
  }>;
  dailyUsage: Array<{
    date: string;
    calls: number;
    tokens: number;
    cost: number;
  }>;
}

export interface BillingPlan {
  id: string;
  name: string;
  description: string;
  monthlyTokenLimit: number;
  pricePerToken: number; // 超出限制后的价格
  monthlyFee: number; // 月费
  features: string[];
  enabled: boolean;
}

// 默认计费方案
export const DEFAULT_BILLING_PLANS: BillingPlan[] = [
  {
    id: "free",
    name: "免费版",
    description: "适合个人用户试用",
    monthlyTokenLimit: 10000,
    pricePerToken: 0.002, // 超出后每1000token的价格
    monthlyFee: 0,
    features: ["基础模型访问", "标准支持"],
    enabled: true,
  },
  {
    id: "basic",
    name: "基础版",
    description: "适合轻度使用的个人用户",
    monthlyTokenLimit: 100000,
    pricePerToken: 0.0015,
    monthlyFee: 9.99,
    features: ["所有模型访问", "优先支持", "使用统计"],
    enabled: true,
  },
  {
    id: "pro",
    name: "专业版",
    description: "适合重度使用的专业用户",
    monthlyTokenLimit: 1000000,
    pricePerToken: 0.001,
    monthlyFee: 29.99,
    features: ["所有模型访问", "优先支持", "详细统计", "API访问"],
    enabled: true,
  },
  {
    id: "enterprise",
    name: "企业版",
    description: "适合企业用户",
    monthlyTokenLimit: -1, // 无限制
    pricePerToken: 0.0008,
    monthlyFee: 99.99,
    features: ["所有功能", "专属支持", "自定义集成", "SLA保证"],
    enabled: true,
  },
];

/**
 * 获取用户使用统计
 */
export async function getUserUsageStats(
  userId: string,
  startDate?: Date,
  endDate?: Date,
): Promise<UserUsageStats> {
  const now = new Date();
  const defaultStartDate =
    startDate || new Date(now.getFullYear(), now.getMonth(), 1);
  const defaultEndDate = endDate || now;

  // 基础统计查询
  const whereClause = {
    userId,
    createdAt: {
      gte: defaultStartDate,
      lte: defaultEndDate,
    },
  };

  const [
    totalCalls,
    successfulCalls,
    failedCalls,
    tokenStats,
    avgResponseTime,
    topModelsData,
  ] = await Promise.all([
    // 总调用次数
    prisma.apiLog.count({ where: whereClause }),

    // 成功调用次数
    prisma.apiLog.count({
      where: { ...whereClause, status: "success" },
    }),

    // 失败调用次数
    prisma.apiLog.count({
      where: { ...whereClause, status: { in: ["error", "timeout"] } },
    }),

    // Token统计
    prisma.apiLog.aggregate({
      where: whereClause,
      _sum: {
        tokensUsed: true,
        promptTokens: true,
        completionTokens: true,
        cost: true,
      },
    }),

    // 平均响应时间
    prisma.apiLog.aggregate({
      where: { ...whereClause, duration: { not: null } },
      _avg: {
        duration: true,
      },
    }),

    // 最常用的模型
    prisma.apiLog.groupBy({
      by: ["model"],
      where: whereClause,
      _count: { id: true },
      _sum: {
        tokensUsed: true,
        cost: true,
      },
      orderBy: {
        _count: { id: "desc" },
      },
      take: 5,
    }),
  ]);

  // 计算每日使用量
  const dailyUsage = await getDailyUsage(
    userId,
    defaultStartDate,
    defaultEndDate,
  );

  const successRate = totalCalls > 0 ? (successfulCalls / totalCalls) * 100 : 0;

  return {
    userId,
    totalCalls,
    successfulCalls,
    failedCalls,
    totalTokens: tokenStats._sum.tokensUsed || 0,
    promptTokens: tokenStats._sum.promptTokens || 0,
    completionTokens: tokenStats._sum.completionTokens || 0,
    totalCost: tokenStats._sum.cost || 0,
    successRate: Math.round(successRate * 100) / 100,
    averageResponseTime: Math.round(avgResponseTime._avg.duration || 0),
    topModels: topModelsData.map((item) => ({
      model: item.model,
      calls: item._count.id,
      tokens: item._sum.tokensUsed || 0,
      cost: item._sum.cost || 0,
    })),
    dailyUsage,
  };
}

/**
 * 获取每日使用量统计
 */
async function getDailyUsage(
  userId: string,
  startDate: Date,
  endDate: Date,
): Promise<
  Array<{ date: string; calls: number; tokens: number; cost: number }>
> {
  const days = Math.ceil(
    (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
  );
  const dailyUsage = [];

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const nextDate = new Date(date);
    nextDate.setDate(nextDate.getDate() + 1);

    const dayStats = await prisma.apiLog.aggregate({
      where: {
        userId,
        createdAt: {
          gte: date,
          lt: nextDate,
        },
      },
      _count: { id: true },
      _sum: {
        tokensUsed: true,
        cost: true,
      },
    });

    dailyUsage.push({
      date: date.toISOString().split("T")[0],
      calls: dayStats._count.id,
      tokens: dayStats._sum.tokensUsed || 0,
      cost: dayStats._sum.cost || 0,
    });
  }

  return dailyUsage;
}

/**
 * 计算用户当月账单
 */
export async function calculateUserBill(
  userId: string,
  planId: string = "free",
  month?: Date,
): Promise<{
  planFee: number;
  usageFee: number;
  totalFee: number;
  tokensUsed: number;
  tokensIncluded: number;
  tokensOverage: number;
  plan: BillingPlan;
}> {
  const now = new Date();
  const billMonth = month || new Date(now.getFullYear(), now.getMonth(), 1);
  const nextMonth = new Date(
    billMonth.getFullYear(),
    billMonth.getMonth() + 1,
    1,
  );

  // 获取计费方案
  const plan =
    DEFAULT_BILLING_PLANS.find((p) => p.id === planId) ||
    DEFAULT_BILLING_PLANS[0];

  // 获取当月使用量
  const usage = await getUserUsageStats(userId, billMonth, nextMonth);

  // 计算费用
  const tokensUsed = usage.totalTokens;
  const tokensIncluded =
    plan.monthlyTokenLimit > 0 ? plan.monthlyTokenLimit : tokensUsed;
  const tokensOverage = Math.max(0, tokensUsed - tokensIncluded);

  const planFee = plan.monthlyFee;
  const usageFee = (tokensOverage / 1000) * plan.pricePerToken;
  const totalFee = planFee + usageFee;

  return {
    planFee,
    usageFee,
    totalFee,
    tokensUsed,
    tokensIncluded,
    tokensOverage,
    plan,
  };
}

/**
 * 检查用户是否超出使用限制
 */
export async function checkUserUsageLimit(
  userId: string,
  planId: string = "free",
): Promise<{
  withinLimit: boolean;
  tokensUsed: number;
  tokensLimit: number;
  usagePercentage: number;
}> {
  const plan =
    DEFAULT_BILLING_PLANS.find((p) => p.id === planId) ||
    DEFAULT_BILLING_PLANS[0];

  // 如果是无限制方案
  if (plan.monthlyTokenLimit <= 0) {
    return {
      withinLimit: true,
      tokensUsed: 0,
      tokensLimit: -1,
      usagePercentage: 0,
    };
  }

  // 获取当月使用量
  const now = new Date();
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const usage = await getUserUsageStats(userId, monthStart);

  const tokensUsed = usage.totalTokens;
  const tokensLimit = plan.monthlyTokenLimit;
  const usagePercentage = (tokensUsed / tokensLimit) * 100;
  const withinLimit = tokensUsed <= tokensLimit;

  return {
    withinLimit,
    tokensUsed,
    tokensLimit,
    usagePercentage: Math.round(usagePercentage * 100) / 100,
  };
}

/**
 * 获取系统整体计费统计
 */
export async function getSystemBillingStats(days: number = 30): Promise<{
  totalRevenue: number;
  totalUsers: number;
  activeUsers: number;
  totalCalls: number;
  totalTokens: number;
  averageRevenuePerUser: number;
  planDistribution: Array<{
    planId: string;
    userCount: number;
    revenue: number;
  }>;
}> {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const [totalUsers, activeUsers, totalCalls, totalTokens, totalCost] =
    await Promise.all([
      prisma.user.count(),
      prisma.user.count({
        where: {
          apiLogs: {
            some: {
              createdAt: {
                gte: startDate,
              },
            },
          },
        },
      }),
      prisma.apiLog.count({
        where: {
          createdAt: {
            gte: startDate,
          },
        },
      }),
      prisma.apiLog.aggregate({
        where: {
          createdAt: {
            gte: startDate,
          },
        },
        _sum: {
          tokensUsed: true,
        },
      }),
      prisma.apiLog.aggregate({
        where: {
          createdAt: {
            gte: startDate,
          },
        },
        _sum: {
          cost: true,
        },
      }),
    ]);

  // 这里简化处理，实际应用中应该从用户订阅表中获取真实的收入数据
  const totalRevenue = totalCost._sum.cost || 0;
  const averageRevenuePerUser =
    activeUsers > 0 ? totalRevenue / activeUsers : 0;

  // 简化的方案分布（实际应用中应该从用户订阅表获取）
  const planDistribution = DEFAULT_BILLING_PLANS.map((plan) => ({
    planId: plan.id,
    userCount: Math.floor(totalUsers / DEFAULT_BILLING_PLANS.length), // 简化分布
    revenue: totalRevenue / DEFAULT_BILLING_PLANS.length, // 简化收入
  }));

  return {
    totalRevenue,
    totalUsers,
    activeUsers,
    totalCalls,
    totalTokens: totalTokens._sum.tokensUsed || 0,
    averageRevenuePerUser,
    planDistribution,
  };
}
